import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/models/get_ask_ai_response.dart';
import 'package:mastercookai/core/data/models/get_ask_ai_thread_messages.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/widgets/custom_appbar.dart';
import '../../../../core/widgets/custom_searchbar.dart';
import '../../../core/data/models/gpt.dart';
import '../../core/providers/gpt/ask_ai_notifier.dart';
import '../cookbook/widgets/custom_desc_text.dart';
import '../../core/helpers/thread_grouper.dart';
import '../../core/network/app_status.dart';
import '../shimer/thread_list_shimmer.dart';

final chatProvider =
    StateNotifierProvider<ChatNotifier, List<String>>((ref) => ChatNotifier());

class ChatNotifier extends StateNotifier<List<String>> {
  ChatNotifier() : super([]);

  void sendMessage(String message) {
    if (message.isEmpty) return;
    state = [...state, message, 'This is a response to: $message'];
  }
}

// ThinkingLoader Widget
class ThinkingLoader extends StatefulWidget {
  final double size;
  final Color dotColor;

  const ThinkingLoader({
    super.key,
    this.size = 40,
    this.dotColor = Colors.blue,
  });

  @override
  State<ThinkingLoader> createState() => _ThinkingLoaderState();
}

class _ThinkingLoaderState extends State<ThinkingLoader>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      3,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.2, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    _startAnimation();
  }

  void _startAnimation() async {
    while (mounted) {
      for (int i = 0; i < _controllers.length; i++) {
        if (mounted) {
          _controllers[i].forward();
          await Future.delayed(const Duration(milliseconds: 150));
        }
      }
      await Future.delayed(const Duration(milliseconds: 300));
      for (int i = 0; i < _controllers.length; i++) {
        if (mounted) {
          _controllers[i].reverse();
        }
      }
      await Future.delayed(const Duration(milliseconds: 500));
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.size.h,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(3, (index) {
          return AnimatedBuilder(
            animation: _animations[index],
            builder: (context, child) {
              return Container(
                width: 8.w,
                height: 8.h,
                margin: EdgeInsets.symmetric(horizontal: 2.w),
                decoration: BoxDecoration(
                  color: widget.dotColor.withValues(alpha: _animations[index].value),
                  shape: BoxShape.circle,
                ),
              );
            },
          );
        }),
      ),
    );
  }
}



class GptScreen extends ConsumerStatefulWidget {
  const GptScreen({super.key});

  @override
  ConsumerState<GptScreen> createState() => _GptScreenState();


  
}

class _GptScreenState extends ConsumerState<GptScreen> {
  // Sample ingredients list
  final List<String> _ingredients = [
    '1 cup butter, divided',
    '1 onion, minced',
    '1 tablespoon minced garlic',
    '1 (15 ounce) can tomato sauce',
    '3 cups heavy cream',
  ];

  // Selected thread state
  Thread? selectedThread;

  // ScrollController for chat messages
  final ScrollController _chatScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // _scrollController.addListener(_scrollListener);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await ref.read(askAiNotifierProvider.notifier).getAskAiThreads(
        context: context,
        loadMore: false,
      );
      final extras = GoRouterState.of(context).extra as Map<String, dynamic>?;
      final Thread? passedThread = extras?['threads'] as Thread?;
      final askAiState = ref.read(askAiNotifierProvider);

      if (askAiState.data != null && askAiState.data!.isNotEmpty) {
        Thread? threadToSelect;

        if (passedThread != null) {
          final index = askAiState.data!
              .indexWhere((cb) => cb.id == passedThread.id);
          if (index != -1) {
            threadToSelect = passedThread;
          }
        }

        // If no passed thread or not found, select first thread (index 0)
        if (threadToSelect == null) {
          threadToSelect = askAiState.data!.first;
        }

        // Set selected thread and fetch its messages
        setState(() {
          selectedThread = threadToSelect;
        });

        // Fetch messages for the selected thread
        if (mounted) {
          await ref.read(threadMessagesNotifierProvider.notifier).fetchThreadsMessages(
            context: context,
            id: threadToSelect.id,
            reset: true,
          );
        }
      }
    });
  }

  @override
  void dispose() {
    _chatScrollController.dispose();
    super.dispose();
  }

  // Handle thread selection from sidebar
  void _onThreadSelected(Thread thread) async {
    setState(() {
      selectedThread = thread;
    });

    // Fetch messages for the selected thread
    if (mounted) {
      await ref.read(threadMessagesNotifierProvider.notifier).fetchThreadsMessages(
        context: context,
        id: thread.id,
        reset: true,
      );
    }
  }

  @override
  Widget build(BuildContext context) {

    final askAiState = ref.watch(askAiNotifierProvider);
    final askAiNotifier = ref.read(askAiNotifierProvider.notifier);
    // final formKey = useMemoized(() => GlobalKey<FormState>());

    final threadMessagesState = ref.watch(threadMessagesNotifierProvider);

    // Auto-scroll when askAI response is received
    ref.listen<AppState<List<Thread>>>(askAiNotifierProvider, (previous, next) {
      if (previous?.status == AppStatus.creating && next.status == AppStatus.createSuccess) {
        // Response received, scroll to bottom to show the new message
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_chatScrollController.hasClients) {
            _chatScrollController.animateTo(
              _chatScrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        });
      }
    });
    final messageController = TextEditingController();
    final TextEditingController searchController = TextEditingController();
    final screenSize = MediaQuery.of(context).size;
    return Scaffold(
      appBar: CustomAppBar(
        title: "Recipe GPT",
        actions: [
          GestureDetector(
            onTap: () {},
            child: Row(
              children: [
                SvgPicture.asset(AssetsManager.gpt_edit),
                SizedBox(width: 8.w),
                Text('New Chat',
                    style: context.theme.textTheme.bodyMedium!.copyWith(
                        color: AppColors.blackTextColor,
                        fontWeight: FontWeight.w400,
                        fontSize: 25.sp)),
              ],
            ),
          ),
          SizedBox(width: 12.w),
          CustomSearchBar(
            controller: searchController,
            hintText: "Search",
            width: 400.w,
          )
        ],
      ),
      body: Stack(
        fit: StackFit.expand,
        children: [
          Image.asset(
            AssetsManager.background_img,
            fit: BoxFit.cover,
          ),
          LayoutBuilder(
            builder: (context, constraints) {
              final isWide = constraints.maxWidth >= 800;
              return Row(
                children: [
                  if (isWide) Sidebar(
                    onThreadSelected: _onThreadSelected,
                  ),
                  Expanded(
                    flex: 7,
                    child: Container(
                      margin: EdgeInsets.symmetric(
                          horizontal: screenSize.width * 0.04,
                          vertical: screenSize.height * 0.05),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[200]!),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _buildChatContent(threadMessagesState),
                          Container(
                            margin: EdgeInsets.symmetric(
                                horizontal: 45.w, vertical: 35.h),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.grey[200]!),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.09),
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Container(
                              margin: EdgeInsets.symmetric(
                                  horizontal: 20.w, vertical: 10.h),
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 20.h,
                                  ),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: TextFormField(
                                          style: context
                                              .theme.textTheme.labelMedium,
                                          controller: messageController,
                                          decoration: InputDecoration(
                                            fontSize: 24.sp
                                            filled: false,
                                            // ensure this is false
                                            fillColor: Colors.transparent,
                                            hintText:
                                                'Give cake Recipe with image',
                                            hintStyle: context.theme
                                                .inputDecorationTheme.hintStyle!
                                                .copyWith(
                                                    fontWeight:
                                                        FontWeight.w400),
                                            errorStyle: context
                                                .theme
                                                .inputDecorationTheme
                                                .errorStyle,
                                            border: InputBorder.none,
                                            focusedBorder: InputBorder.none,
                                            enabledBorder: InputBorder.none,
                                            disabledBorder: InputBorder.none,
                                            isDense: true,
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                                    horizontal: 12,
                                                    vertical: 14),
                                          ),
                                          inputFormatters: [
                                            FilteringTextInputFormatter.allow(
                                                RegExp(r'[a-zA-Z0-9\s,]')),
                                          ],
                                          onFieldSubmitted: (value) {
                                            ref
                                                .read(chatProvider.notifier)
                                                .sendMessage(value);
                                            messageController.clear();
                                          },
                                          keyboardType: TextInputType.text,
                                        ),
                                      ),
                                      SizedBox(width: 8.w),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 20.h,
                                  ),
                                  Row(
                                    children: [
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 20.w, vertical: 10.h),
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          border: Border.all(
                                              color: Colors.grey[200]!),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black
                                                  .withOpacity(0.05),
                                              blurRadius: 10,
                                              offset: const Offset(0, 4),
                                            ),
                                          ],
                                        ),
                                        child: Row(
                                          children: [
                                            SvgPicture.asset(
                                                AssetsManager.think,
                                                width: 35.w,
                                                height: 35.h),
                                            SizedBox(
                                              width: 10.w,
                                            ),
                                            Text("Think before responding",
                                                style: context
                                                    .theme.textTheme.bodyMedium!
                                                    .copyWith(
                                                        color: AppColors
                                                            .primaryGreyColor,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        fontSize: 24.sp))
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        width: 20.w,
                                      ),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 20.w, vertical: 10.h),
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          border: Border.all(
                                              color: Colors.grey[200]!),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black
                                                  .withOpacity(0.05),
                                              blurRadius: 10,
                                              offset: const Offset(0, 4),
                                            ),
                                          ],
                                        ),
                                        child: Row(
                                          children: [
                                            SvgPicture.asset(
                                                AssetsManager.web_search,
                                                width: 35.w,
                                                height: 35.h),
                                            SizedBox(
                                              width: 10.w,
                                            ),
                                            Text("Search the web",
                                                style: context
                                                    .theme.textTheme.bodyMedium!
                                                    .copyWith(
                                                        color: AppColors
                                                            .primaryGreyColor,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        fontSize: 24.sp))
                                          ],
                                        ),
                                      ),
                                      Spacer(),
                                      GestureDetector(
                                        child: Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 10.w, vertical: 10.h),
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            border: Border.all(
                                                color: Colors.grey[200]!),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black
                                                    .withOpacity(0.05),
                                                blurRadius: 10,
                                                offset: const Offset(0, 4),
                                              ),
                                            ],
                                          ),
                                          child: SvgPicture.asset(
                                              AssetsManager.send_msg,
                                              width: 35.w,
                                              height: 35.h),
                                        ),
                                        onTap: () {
                                          askAiNotifier.askAi(
                                            context,
                                            selectedThread?.id, // Use selected thread ID, null if no thread selected
                                            messageController.text.toString(),
                                            onNewThreadCreated: (threadData) {
                                              // When a new thread is created, find and select it
                                              final askAiState = ref.read(askAiNotifierProvider);
                                              if (askAiState.data != null && askAiState.data!.isNotEmpty) {
                                                // Find the thread that matches the new thread data
                                                final newThread = askAiState.data!.firstWhere(
                                                  (thread) => thread.id == threadData.id,
                                                  orElse: () => askAiState.data!.first,
                                                );
                                                setState(() {
                                                  selectedThread = newThread;
                                                });
                                              }
                                            }
                                          );

                                          // Auto-scroll to bottom to show thinking loader
                                          Future.delayed(const Duration(milliseconds: 100), () {
                                            if (_chatScrollController.hasClients) {
                                              _chatScrollController.animateTo(
                                                _chatScrollController.position.maxScrollExtent,
                                                duration: const Duration(milliseconds: 300),
                                                curve: Curves.easeOut,
                                              );
                                            }
                                          });

                                          // ref
                                          //     .read(chatProvider.notifier)
                                          //     .sendMessage(
                                          //         messageController.text);
                                          messageController.clear();
                                        },
                                      ),
                                      SizedBox(width: 20.w),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 20.h,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  // Build chat content based on thread messages
  Widget _buildChatContent(AppState<List<UserMessage>> threadMessagesState) {
    // Show loading state
    if (threadMessagesState.status == AppStatus.loading) {
      return const Expanded(
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Show error state
    if (threadMessagesState.status == AppStatus.error) {
      return Expanded(
        child: Center(
          child: Text(
            threadMessagesState.errorMessage ?? 'Failed to load messages',
            style: TextStyle(
              color: Colors.red,
              fontSize: 16.sp,
            ),
          ),
        ),
      );
    }

    // Show empty state or welcome screen
    if (threadMessagesState.status == AppStatus.empty ||
        (threadMessagesState.data?.isEmpty ?? true)) {
      return const WelcomeScreen();
    }

    // Show messages
    final messages = threadMessagesState.data!;
    final askAiState = ref.watch(askAiNotifierProvider);
    final isAskAiLoading = askAiState.status == AppStatus.creating;

    return Expanded(
      child: ListView.builder(
        controller: _chatScrollController,
        padding: const EdgeInsets.all(16),
        itemCount: (messages.length * 2) + (isAskAiLoading ? 1 : 0), // Add 1 for thinking loader
        itemBuilder: (context, index) {
          // Show thinking loader at the end when askAI is loading
          if (isAskAiLoading && index == (messages.length * 2)) {
            return Align(
              alignment: Alignment.centerLeft,
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.7,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.grey[200]!),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'AI is thinking',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    const ThinkingLoader(
                      size: 20,
                      dotColor: Colors.blue,
                    ),
                  ],
                ),
              ),
            );
          }

          // Handle regular messages
          final messageIndex = index ~/ 2;
          final isPrompt = index.isEven;
          final message = messages[messageIndex];

          if (isPrompt) {
            // User prompt (right side)
            return Align(
              alignment: Alignment.centerRight,
              child: Container(
                margin: const EdgeInsets.symmetric(vertical: 4),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.lightestGreyColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  message.prompt,
                  style: context.theme.textTheme.bodyMedium!.copyWith(
                    color: AppColors.primaryGreyColor,
                    fontWeight: FontWeight.w400,
                    fontSize: 22.sp,
                  ),
                ),
              ),
            );
          } else {
            // AI response (left side)
            return Align(
              alignment: Alignment.centerLeft,
              child: Container(
                margin: const EdgeInsets.symmetric(vertical: 4),
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHtmlText(message.response),
                    // Display image if available
                    if (message.imageUrl != null && message.imageUrl!.isNotEmpty) ...[
                      SizedBox(height: 12.h),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          message.imageUrl!,
                          width: 450.w,
                          height: 400.h,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 450.w,
                              height: 400.h,
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Icon(
                                Icons.broken_image,
                                color: Colors.grey,
                                size: 50,
                              ),
                            );
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Container(
                              width: 450.w,
                              height: 400.h,
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Center(
                                child: CircularProgressIndicator(),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                    SizedBox(height: 8.h),
                    // Action icons row
                    SizedBox(
                      width: 230.w,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SvgPicture.asset(
                            AssetsManager.share_recipe,
                            height: 40.h,
                            width: 40.w,
                          ),
                          SvgPicture.asset(
                            AssetsManager.download,
                            height: 40.h,
                            width: 40.w,
                          ),
                          SvgPicture.asset(
                            AssetsManager.rename,
                            height: 40.h,
                            width: 40.w,
                          ),
                          SvgPicture.asset(
                            AssetsManager.dlt_recipe,
                            height: 40.h,
                            width: 40.w,
                          ),
                          SvgPicture.asset(
                            AssetsManager.reload,
                            height: 40.h,
                            width: 40.w,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          }
        },
      ),
    );
  }

  // Helper method to convert HTML response to styled text widgets
  Widget _buildHtmlText(String htmlString) {
    if (htmlString.isEmpty) {
      return const SizedBox.shrink();
    }

    // Parse HTML and convert to styled text
    return _parseHtmlToWidget(htmlString);
  }

  Widget _parseHtmlToWidget(String htmlString) {
    List<Widget> widgets = [];

    // Process HTML content sequentially to maintain original order
    _processHtmlSequentially(htmlString, widgets);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  void _processHtmlSequentially(String html, List<Widget> widgets) {
    // Split HTML into segments while preserving order
    RegExp htmlElementRegex = RegExp(r'(<p[^>]*>.*?</p>|<ul[^>]*>.*?</ul>|<ol[^>]*>.*?</ol>)', caseSensitive: false, dotAll: true);

    int lastEnd = 0;

    for (Match match in htmlElementRegex.allMatches(html)) {
      // Add any text before this element
      if (match.start > lastEnd) {
        String beforeText = html.substring(lastEnd, match.start).trim();
        if (beforeText.isNotEmpty) {
          String cleanText = _cleanHtmlText(beforeText);
          if (cleanText.isNotEmpty) {
            widgets.add(
              Text(
                cleanText,
                style: context.theme.textTheme.bodyMedium!.copyWith(
                  color: AppColors.primaryGreyColor,
                  fontWeight: FontWeight.w400,
                  fontSize: 22.sp,
                ),
              ),
            );
            widgets.add(SizedBox(height: 8.h));
          }
        }
      }

      String element = match.group(0)!;

      // Process the matched element based on its type
      if (element.toLowerCase().startsWith('<p')) {
        _processParagraphElement(element, widgets);
      } else if (element.toLowerCase().startsWith('<ul')) {
        _processUnorderedListElement(element, widgets);
      } else if (element.toLowerCase().startsWith('<ol')) {
        _processOrderedListElement(element, widgets);
      }

      lastEnd = match.end;
    }

    // Add any remaining text after the last element
    if (lastEnd < html.length) {
      String remainingText = html.substring(lastEnd).trim();
      if (remainingText.isNotEmpty) {
        String cleanText = _cleanHtmlText(remainingText);
        if (cleanText.isNotEmpty) {
          widgets.add(
            Text(
              cleanText,
              style: context.theme.textTheme.bodyMedium!.copyWith(
                color: AppColors.primaryGreyColor,
                fontWeight: FontWeight.w400,
                fontSize: 22.sp,
              ),
            ),
          );
        }
      }
    }

    // Remove the last spacing if it exists
    if (widgets.isNotEmpty && widgets.last is SizedBox) {
      widgets.removeLast();
    }
  }

  void _processParagraphElement(String element, List<Widget> widgets) {
    // Extract text from paragraph tags
    String text = element.replaceAll(RegExp(r'</?p[^>]*>', caseSensitive: false), '');
    String cleanText = _cleanHtmlText(text);

    if (cleanText.trim().isNotEmpty) {
      widgets.add(
        Text(
          cleanText.trim(),
          style: context.theme.textTheme.bodyMedium!.copyWith(
            color: AppColors.primaryGreyColor,
            fontWeight: FontWeight.w400,
            fontSize: 22.sp,
          ),
        ),
      );
      widgets.add(SizedBox(height: 8.h));
    }
  }

  void _processUnorderedListElement(String element, List<Widget> widgets) {
    // Extract list items from ul element
    RegExp liRegex = RegExp(r'<li[^>]*>(.*?)</li>', caseSensitive: false, dotAll: true);

    for (Match liMatch in liRegex.allMatches(element)) {
      String itemText = liMatch.group(1) ?? '';
      itemText = _cleanHtmlText(itemText);

      if (itemText.trim().isNotEmpty) {
        widgets.add(
          Padding(
            padding: EdgeInsets.only(left: 16.w, bottom: 4.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "• ",
                  style: context.theme.textTheme.bodyMedium!.copyWith(
                    color: AppColors.primaryGreyColor,
                    fontWeight: FontWeight.w400,
                    fontSize: 22.sp,
                  ),
                ),
                Expanded(
                  child: Text(
                    itemText.trim(),
                    style: context.theme.textTheme.bodyMedium!.copyWith(
                      color: AppColors.primaryGreyColor,
                      fontWeight: FontWeight.w400,
                      fontSize: 22.sp,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }
    }

    // Add spacing after list
    widgets.add(SizedBox(height: 8.h));
  }

  void _processOrderedListElement(String element, List<Widget> widgets) {
    // Extract list items from ol element
    RegExp liRegex = RegExp(r'<li[^>]*>(.*?)</li>', caseSensitive: false, dotAll: true);

    for (Match liMatch in liRegex.allMatches(element)) {
      String itemText = liMatch.group(1) ?? '';
      itemText = _cleanHtmlText(itemText);

      if (itemText.trim().isNotEmpty) {
        widgets.add(
          Padding(
            padding: EdgeInsets.only(left: 16.w, bottom: 4.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "• ",
                  style: context.theme.textTheme.bodyMedium!.copyWith(
                    color: AppColors.primaryGreyColor,
                    fontWeight: FontWeight.w400,
                    fontSize: 22.sp,
                  ),
                ),
                Expanded(
                  child: Text(
                    itemText.trim(),
                    style: context.theme.textTheme.bodyMedium!.copyWith(
                      color: AppColors.primaryGreyColor,
                      fontWeight: FontWeight.w400,
                      fontSize: 22.sp,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }
    }

    // Add spacing after list
    widgets.add(SizedBox(height: 8.h));
  }

  String _cleanHtmlText(String text) {
    // Remove all HTML tags and decode entities
    String cleaned = text
        .replaceAll(RegExp(r'<[^>]*>'), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();

    return _decodeHtmlEntities(cleaned);
  }



  String _decodeHtmlEntities(String text) {
    return text
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&quot;', '"')
        .replaceAll('&#39;', "'")
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&hellip;', '...')
        .replaceAll('&mdash;', '—')
        .replaceAll('&ndash;', '–')
        .replaceAll('&copy;', '©')
        .replaceAll('&reg;', '®')
        .replaceAll('&trade;', '™');
  }
}

final List<Gpt> mealSections = [
  Gpt(title: 'Today', items: ["Shepherd's Pie", "Cottage Pie"]),
  Gpt(
    title: 'Previous 7 days',
    items: [
      "Cottage Pie",
      "Bangers and Mash",
      "Toad in the Hole",
      "Sunday Roast"
    ],
  ),
  Gpt(
    title: 'Previous 30 days',
    items: ["Fish and Chips", "Steak and Kidney Pie"],
  ),
];

class Sidebar extends ConsumerStatefulWidget {
  final Function(Thread)? onThreadSelected;

  const Sidebar({super.key, this.onThreadSelected});

  @override
  ConsumerState<Sidebar> createState() => _SidebarState();
}

class _SidebarState extends ConsumerState<Sidebar> {
  int? selectedIndex;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final askAiState = ref.read(askAiNotifierProvider);
      if (askAiState.hasMore && askAiState.status != AppStatus.loadingMore) {
        ref.read(askAiNotifierProvider.notifier).getAskAiThreads(
          context: context,
          loadMore: true,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final TextEditingController searchController = TextEditingController();
    final askAiState = ref.watch(askAiNotifierProvider);

    return Expanded(
      flex: 2,
      child: Container(
        color: Colors.white,
        child: Column(
          children: [
            SizedBox(height: 20.h),
            CustomSearchBar(
              controller: searchController,
              hintText: "Search",
              width: 800.w,
            ),
            Expanded(
              child: _buildThreadList(askAiState),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThreadList(AppState<List<Thread>> askAiState) {
    // Show shimmer during initial loading
    if (askAiState.status == AppStatus.loading && (askAiState.data?.isEmpty ?? true)) {
      return const ThreadListShimmer(itemCount: 8);
    }

    // Show error state
    if (askAiState.status == AppStatus.error) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(20.w),
          child: Text(
            askAiState.errorMessage ?? 'Failed to load threads',
            style: TextStyle(
              color: Colors.red,
              fontSize: 16.sp,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    // Show empty state
    if (askAiState.status == AppStatus.empty || (askAiState.data?.isEmpty ?? true)) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(20.w),
          child: Text(
            'No threads found',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16.sp,
            ),
          ),
        ),
      );
    }

    // Group threads by time period
    final threadSections = ThreadGrouper.groupThreadsByTimePeriod(askAiState.data!);
    final totalItemCount = ThreadGrouper.calculateTotalItemCount(threadSections);

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      itemCount: totalItemCount + (askAiState.hasMore ? 1 : 0), // Add 1 for loading indicator
      itemBuilder: (context, index) {
        // Show loading indicator at the bottom for pagination
        if (index >= totalItemCount) {
          return askAiState.status == AppStatus.loadingMore
              ? Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  child: Center(
                    child: SizedBox(
                      width: 20.w,
                      height: 20.h,
                      child: const CircularProgressIndicator(strokeWidth: 2),
                    ),
                  ),
                )
              : const SizedBox.shrink();
        }

        final item = ThreadGrouper.getItemAtIndex(threadSections, index);

        if (item is ThreadSection) {
          // Render section header
          return _buildSectionHeader(item.title);
        } else if (item is Thread) {
          // Render thread item
          return _buildThreadItem(item, index);
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: EdgeInsets.only(top: 24.h, bottom: 12.h),
      child: Text(
        title,
        style: context.theme.textTheme.bodyMedium!.copyWith(
          color: AppColors.textGreyColor,
          fontWeight: FontWeight.w400,
          fontSize: 22.sp,
        ),
      ),
    );
  }

  Widget _buildThreadItem(Thread thread, int index) {
    final isSelected = selectedIndex == index;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedIndex = index;
        });
        // Call the callback to notify parent about thread selection
        widget.onThreadSelected?.call(thread);
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 8.h),
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color.fromARGB(255, 68, 68, 68).withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                thread.title,
                style: context.theme.textTheme.bodyMedium!.copyWith(
                  color: AppColors.blackTextColor,
                  fontWeight: FontWeight.w400,
                  fontSize: 22.sp,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            // Show three dots menu for thread actions
            Visibility(
              visible: isSelected,
              child: PopupMenuButton<String>(
                icon: Icon(
                  Icons.more_horiz,
                  color: AppColors.blackColor,
                  size: 30.sp,
                ),
              onSelected: (value) {
                // Handle menu actions
                switch (value) {
                  case 'share':
                    // Handle share thread
                    break;
                  case 'download':
                    // Handle save to cookbook
                    break;
                  case 'rename':
                    // Handle rename thread
                    break;
                  case 'delete':
                    // Handle delete thread
                    break;
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem<String>(
                  value: 'share',
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        AssetsManager.share_recipe,
                        height: 40.h,
                        width: 40.w,
                      ),
                      SizedBox(width: 15.w),
                      CustomDescText(
                        desc: "Share",
                        textColor: Colors.black,
                        size: 24.sp,
                      ),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'download',
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        AssetsManager.download,
                        height: 40.h,
                        width: 40.w,
                      ),
                      SizedBox(width: 15.w),
                      CustomDescText(
                        desc: "Save to Cookbook",
                        textColor: Colors.black,
                        size: 24.sp,
                      ),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'rename',
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        AssetsManager.rename,
                        height: 40.h,
                        width: 40.w,
                      ),
                      SizedBox(width: 15.w),
                      CustomDescText(
                        desc: "Rename",
                        textColor: Colors.black,
                        size: 24.sp,
                      ),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'delete',
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        AssetsManager.dlt_recipe,
                        height: 40.h,
                        width: 40.w,
                      ),
                      SizedBox(width: 15.w),
                      CustomDescText(
                        desc: "Delete",
                        textColor: AppColors.primaryColor,
                        size: 24.sp,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            ),
          ],
        ),
      ),
    );
  }
}

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text('Meet Your Personal AI-Powered Kitchen Assistant',
                textAlign: TextAlign.center,
                style: context.theme.textTheme.bodyMedium!.copyWith(
                  color: Colors.black,
                  fontSize: 50.sp,
                  fontWeight: FontWeight.w700,
                )),
            SizedBox(height: 20.h),
            Text(
                'Simply type a recipe idea or some ingredients you have on hand and DishGen\'s AI will\ninstantly generate an all-new recipe on demand…',
                textAlign: TextAlign.center,
                style: context.theme.textTheme.bodyMedium!.copyWith(
                  color: Colors.black,
                  fontSize: 26.sp,
                  fontWeight: FontWeight.w400,
                )),
          ],
        ),
      ),
    );
  }
}
